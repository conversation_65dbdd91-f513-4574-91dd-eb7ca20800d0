// Simple test script to verify Firebase Functions work correctly
const axios = require('axios');

// Use emulator URL for local testing
const FUNCTIONS_URL = process.env.FUNCTIONS_URL || 'http://localhost:5001/playlist-importer/us-central1/api';

async function testFirebaseFunctions() {
  console.log('🔥 Testing Firebase Functions...');
  console.log(`Using URL: ${FUNCTIONS_URL}`);
  
  try {
    // Test health endpoint
    console.log('\n1. Testing health endpoint...');
    const healthResponse = await axios.get(`${FUNCTIONS_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data);
    
    // Test auth status endpoint
    console.log('\n2. Testing auth status endpoint...');
    const authResponse = await axios.get(`${FUNCTIONS_URL}/auth/status?userId=test_user`);
    console.log('✅ Auth status endpoint working:', authResponse.data);
    
    // Test Spotify playlists endpoint (should fail without auth)
    console.log('\n3. Testing Spotify playlists endpoint (should return 401)...');
    try {
      await axios.get(`${FUNCTIONS_URL}/playlists/spotify?userId=test_user`);
      console.log('❌ Expected 401 error but got success');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Correctly returned 401 for unauthenticated request');
      } else {
        console.log('❌ Unexpected error:', error.message);
      }
    }
    
    console.log('\n🎉 All Firebase Functions tests passed!');
    console.log('\n📝 Next steps:');
    console.log('1. Set up your Spotify and Tidal API credentials');
    console.log('2. Test the authentication flow');
    console.log('3. Deploy to Firebase: npm run firebase:deploy');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure Firebase emulators are running with: npm run firebase:serve');
    }
  }
}

// Run tests
testFirebaseFunctions();
