{"hosting": {"public": "client/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/**", "function": "api"}, {"source": "**", "destination": "/index.html"}], "headers": [{"source": "/static/**", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}, "functions": {"source": "functions", "runtime": "nodejs18", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "emulators": {"functions": {"port": 5001}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}}}