import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './App.css';
import AuthSection from './components/AuthSection';
import PlaylistSelector from './components/PlaylistSelector';
import ImportProgress from './components/ImportProgress';

// Use Firebase Functions URL or local development
const API_BASE = process.env.REACT_APP_API_BASE ||
  (process.env.NODE_ENV === 'production'
    ? '/api'
    : 'http://localhost:5001/playlist-importer/us-central1/api');

function App() {
  const [authStatus, setAuthStatus] = useState({ spotify: false, tidal: false });
  const [spotifyPlaylists, setSpotifyPlaylists] = useState([]);
  const [selectedPlaylists, setSelectedPlaylists] = useState([]);
  const [importStatus, setImportStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState(null);

  // Generate or retrieve user ID for session management
  useEffect(() => {
    let storedUserId = localStorage.getItem('playlistImporterUserId');
    if (!storedUserId) {
      storedUserId = 'user_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('playlistImporterUserId', storedUserId);
    }
    setUserId(storedUserId);
  }, []);

  useEffect(() => {
    if (userId) {
      checkAuthStatus();

      // Check for auth success/error in URL params
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('spotify_auth') === 'success') {
        setAuthStatus(prev => ({ ...prev, spotify: true }));
        window.history.replaceState({}, document.title, window.location.pathname);
      }
      if (urlParams.get('tidal_auth') === 'success') {
        setAuthStatus(prev => ({ ...prev, tidal: true }));
        window.history.replaceState({}, document.title, window.location.pathname);
      }
      if (urlParams.get('error')) {
        alert('Authentication error: ' + urlParams.get('error'));
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  }, [userId]);

  useEffect(() => {
    if (authStatus.spotify && userId) {
      fetchSpotifyPlaylists();
    }
  }, [authStatus.spotify, userId]);

  const checkAuthStatus = async () => {
    if (!userId) return;

    try {
      const response = await axios.get(`${API_BASE}/auth/status?userId=${userId}`);
      setAuthStatus(response.data);
    } catch (error) {
      console.error('Error checking auth status:', error);
    }
  };

  const fetchSpotifyPlaylists = async () => {
    if (!userId) return;

    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE}/playlists/spotify?userId=${userId}`);
      setSpotifyPlaylists(response.data);
    } catch (error) {
      console.error('Error fetching playlists:', error);
      alert('Failed to fetch Spotify playlists');
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (selectedPlaylists.length === 0) {
      alert('Please select at least one playlist to import');
      return;
    }

    if (!userId) {
      alert('User session not found. Please refresh the page.');
      return;
    }

    try {
      setLoading(true);
      setImportStatus({ status: 'importing', progress: 0 });

      const response = await axios.post(`${API_BASE}/import/playlists`, {
        playlistIds: selectedPlaylists,
        userId: userId
      });

      setImportStatus({
        status: 'completed',
        progress: 100,
        results: response.data
      });
    } catch (error) {
      console.error('Import error:', error);
      setImportStatus({
        status: 'error',
        error: error.response?.data?.error || 'Import failed'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    if (!userId) return;

    try {
      await axios.post(`${API_BASE}/auth/logout`, { userId });
      setAuthStatus({ spotify: false, tidal: false });
      setSpotifyPlaylists([]);
      setSelectedPlaylists([]);
      setImportStatus(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🎵 Playlist Importer</h1>
        <p>Import your playlists from Spotify to Tidal</p>
      </header>

      <main className="App-main">
        <AuthSection
          authStatus={authStatus}
          onLogout={handleLogout}
          apiBase={API_BASE}
          userId={userId}
        />

        {authStatus.spotify && authStatus.tidal && (
          <>
            <PlaylistSelector
              playlists={spotifyPlaylists}
              selectedPlaylists={selectedPlaylists}
              onSelectionChange={setSelectedPlaylists}
              loading={loading}
              onImport={handleImport}
            />

            {importStatus && (
              <ImportProgress importStatus={importStatus} />
            )}
          </>
        )}
      </main>
    </div>
  );
}

export default App;
