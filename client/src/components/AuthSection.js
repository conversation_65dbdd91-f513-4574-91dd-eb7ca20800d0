import React from 'react';
import './AuthSection.css';

const AuthSection = ({ authStatus, onLogout, apiBase, userId }) => {
  const handleSpotifyAuth = () => {
    if (!userId) {
      alert('User session not found. Please refresh the page.');
      return;
    }
    window.location.href = `${apiBase}/auth/spotify?userId=${userId}`;
  };

  const handleTidalAuth = () => {
    if (!userId) {
      alert('User session not found. Please refresh the page.');
      return;
    }
    window.location.href = `${apiBase}/auth/tidal?userId=${userId}`;
  };

  return (
    <section className="auth-section">
      <h2>Authentication</h2>
      <div className="auth-grid">
        <div className="auth-card">
          <div className="auth-header">
            <img 
              src="https://developer.spotify.com/assets/branding-guidelines/<EMAIL>" 
              alt="Spotify" 
              className="service-icon"
            />
            <h3>Spotify</h3>
          </div>
          <div className="auth-status">
            {authStatus.spotify ? (
              <div className="status-connected">
                <span className="status-indicator connected"></span>
                Connected
              </div>
            ) : (
              <div className="status-disconnected">
                <span className="status-indicator disconnected"></span>
                Not connected
              </div>
            )}
          </div>
          <div className="auth-actions">
            {!authStatus.spotify ? (
              <button 
                className="auth-button spotify-button"
                onClick={handleSpotifyAuth}
              >
                Connect to Spotify
              </button>
            ) : (
              <span className="auth-success">✓ Ready to import from</span>
            )}
          </div>
        </div>

        <div className="auth-card">
          <div className="auth-header">
            <div className="service-icon tidal-icon">T</div>
            <h3>Tidal</h3>
          </div>
          <div className="auth-status">
            {authStatus.tidal ? (
              <div className="status-connected">
                <span className="status-indicator connected"></span>
                Connected
              </div>
            ) : (
              <div className="status-disconnected">
                <span className="status-indicator disconnected"></span>
                Not connected
              </div>
            )}
          </div>
          <div className="auth-actions">
            {!authStatus.tidal ? (
              <button 
                className="auth-button tidal-button"
                onClick={handleTidalAuth}
              >
                Connect to Tidal
              </button>
            ) : (
              <span className="auth-success">✓ Ready to import to</span>
            )}
          </div>
        </div>
      </div>

      {authStatus.spotify && authStatus.tidal && (
        <div className="auth-complete">
          <p>🎉 Both services connected! You can now import playlists.</p>
          <button className="logout-button" onClick={onLogout}>
            Disconnect All
          </button>
        </div>
      )}

      {(!authStatus.spotify || !authStatus.tidal) && (
        <div className="auth-instructions">
          <p>Please connect to both Spotify and Tidal to start importing playlists.</p>
        </div>
      )}
    </section>
  );
};

export default AuthSection;
