// Simple test script to verify the server starts correctly
const axios = require('axios');

async function testServer() {
  try {
    console.log('Testing server health endpoint...');
    const response = await axios.get('http://localhost:3001/health');
    console.log('✅ Server health check passed:', response.data);
    
    console.log('Testing auth status endpoint...');
    const authResponse = await axios.get('http://localhost:3001/auth/status');
    console.log('✅ Auth status endpoint working:', authResponse.data);
    
    console.log('🎉 All basic tests passed!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the server is running with: npm run server');
    }
  }
}

testServer();
