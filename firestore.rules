rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read/write their own session data
    // In production, you should implement proper authentication
    match /sessions/{userId} {
      allow read, write: if true;
    }
    
    // Allow reading/writing import status documents
    match /imports/{importId} {
      allow read, write: if true;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
