{"name": "passport-spotify", "version": "2.0.0", "description": "Spotify authentication strategy for Passport.", "author": {"name": "<PERSON>", "url": "https://github.com/jmperez/"}, "repository": {"type": "git", "url": "https://github.com/jmperez/passport-spotify.git"}, "keywords": ["node", "passport", "spotify"], "bugs": {"url": "https://github.com/jmperez/passport-spotify/issues"}, "main": "./lib/passport-spotify", "engines": {"node": ">= 0.8.0"}, "config": {"blanket": {"pattern": "//^(?!.*node_modules.*$).*lib//"}}, "scripts": {"test": "make coveralls"}, "dependencies": {"passport-oauth": "1.0.0", "querystring": "~0.2.0", "util": "~0.12.0"}, "devDependencies": {"mocha": "5.2.0", "should": "13.2.3", "sinon": "7.4.2", "blanket": "~1.2.3", "mocha-lcov-reporter": "1.3.0", "coveralls": "~3.1.0"}}