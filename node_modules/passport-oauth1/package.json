{"name": "passport-oauth1", "version": "1.3.0", "description": "OAuth 1.0 authentication strategy for Passport.", "keywords": ["passport", "auth", "authn", "authentication", "authz", "authorization", "o<PERSON>h"], "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "repository": {"type": "git", "url": "git://github.com/jared<PERSON>son/passport-oauth1.git"}, "bugs": {"url": "http://github.com/jared<PERSON>son/passport-oauth1/issues"}, "funding": {"type": "github", "url": "https://github.com/sponsors/jared<PERSON>son"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {"passport-strategy": "1.x.x", "oauth": "0.9.x", "utils-merge": "1.x.x"}, "devDependencies": {"make-node": "0.4.6", "mocha": "2.x.x", "chai": "2.x.x", "chai-passport-strategy": "1.x.x"}, "engines": {"node": ">= 0.4.0"}, "scripts": {"test": "make test"}}