{"name": "playlist-importer-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for Playlist Importer", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0", "express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.5.0", "express-session": "^1.17.3", "@tidal-music/auth": "^1.0.0", "@tidal-music/api": "^1.0.0", "@tidal-music/common": "^1.0.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/express-session": "^1.17.7", "firebase-functions-test": "^3.1.0"}, "private": true}