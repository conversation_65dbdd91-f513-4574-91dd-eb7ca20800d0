import express from 'express';
import axios from 'axios';
import * as admin from 'firebase-admin';
import { init, credentialsProvider } from '@tidal-music/auth';

const router = express.Router();

// Initialize Tidal Auth
const tidalAuth = init({
  clientId: process.env.TIDAL_CLIENT_ID || '',
  clientSecret: process.env.TIDAL_CLIENT_SECRET || '',
  scopes: ['r_usr', 'w_usr', 'w_sub']
});

// Helper function to get/set user session data
async function getUserSession(userId: string) {
  const db = admin.firestore();
  const userDoc = await db.collection('sessions').doc(userId).get();
  return userDoc.exists ? userDoc.data() : null;
}

async function setUserSession(userId: string, data: any) {
  const db = admin.firestore();
  await db.collection('sessions').doc(userId).set(data, { merge: true });
}

// Spotify OAuth endpoints
router.get('/spotify', async (req, res) => {
  try {
    const userId = req.query.userId as string || 'anonymous';
    const state = `${userId}_${Date.now()}`;
    
    // Store state for verification
    await setUserSession(userId, { spotifyState: state });
    
    const scope = 'playlist-read-private playlist-read-collaborative user-read-private user-read-email';
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.SPOTIFY_CLIENT_ID || '',
      scope: scope,
      redirect_uri: `${process.env.FIREBASE_FUNCTION_URL}/api/auth/spotify/callback`,
      state: state
    });
    
    res.redirect(`https://accounts.spotify.com/authorize?${params.toString()}`);
  } catch (error) {
    console.error('Spotify auth error:', error);
    res.redirect(`${process.env.CLIENT_URL}?error=spotify_auth_failed`);
  }
});

router.get('/spotify/callback', async (req, res) => {
  try {
    const { code, state } = req.query;
    
    if (!state || !code) {
      return res.redirect(`${process.env.CLIENT_URL}?error=invalid_request`);
    }
    
    const [userId] = (state as string).split('_');
    const session = await getUserSession(userId);
    
    if (!session || session.spotifyState !== state) {
      return res.redirect(`${process.env.CLIENT_URL}?error=state_mismatch`);
    }
    
    // Exchange code for token
    const response = await axios.post('https://accounts.spotify.com/api/token', 
      new URLSearchParams({
        grant_type: 'authorization_code',
        code: code as string,
        redirect_uri: `${process.env.FIREBASE_FUNCTION_URL}/api/auth/spotify/callback`,
        client_id: process.env.SPOTIFY_CLIENT_ID || '',
        client_secret: process.env.SPOTIFY_CLIENT_SECRET || ''
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    // Store tokens
    await setUserSession(userId, {
      spotifyToken: response.data.access_token,
      spotifyRefreshToken: response.data.refresh_token,
      spotifyTokenExpiry: Date.now() + (response.data.expires_in * 1000)
    });
    
    res.redirect(`${process.env.CLIENT_URL}?spotify_auth=success&userId=${userId}`);
  } catch (error) {
    console.error('Spotify callback error:', error);
    res.redirect(`${process.env.CLIENT_URL}?error=spotify_auth_failed`);
  }
});

// Tidal OAuth endpoints using Tidal SDK
router.get('/tidal', async (req, res) => {
  try {
    const userId = req.query.userId as string || 'anonymous';
    const state = `${userId}_${Date.now()}`;
    
    // Store state for verification
    await setUserSession(userId, { tidalState: state });
    
    // Generate Tidal auth URL using SDK
    const authUrl = tidalAuth.getAuthUrl({
      redirectUri: `${process.env.FIREBASE_FUNCTION_URL}/api/auth/tidal/callback`,
      state: state
    });
    
    res.redirect(authUrl);
  } catch (error) {
    console.error('Tidal auth error:', error);
    res.redirect(`${process.env.CLIENT_URL}?error=tidal_auth_failed`);
  }
});

router.get('/tidal/callback', async (req, res) => {
  try {
    const { code, state } = req.query;
    
    if (!state || !code) {
      return res.redirect(`${process.env.CLIENT_URL}?error=invalid_request`);
    }
    
    const [userId] = (state as string).split('_');
    const session = await getUserSession(userId);
    
    if (!session || session.tidalState !== state) {
      return res.redirect(`${process.env.CLIENT_URL}?error=state_mismatch`);
    }
    
    // Exchange code for token using Tidal SDK
    const credentials = await tidalAuth.getCredentials({
      code: code as string,
      redirectUri: `${process.env.FIREBASE_FUNCTION_URL}/api/auth/tidal/callback`
    });
    
    // Store Tidal credentials
    await setUserSession(userId, {
      tidalCredentials: credentials,
      tidalTokenExpiry: Date.now() + (credentials.expiresIn * 1000)
    });
    
    res.redirect(`${process.env.CLIENT_URL}?tidal_auth=success&userId=${userId}`);
  } catch (error) {
    console.error('Tidal callback error:', error);
    res.redirect(`${process.env.CLIENT_URL}?error=tidal_auth_failed`);
  }
});

// Check authentication status
router.get('/status', async (req, res) => {
  try {
    const userId = req.query.userId as string || 'anonymous';
    const session = await getUserSession(userId);
    
    if (!session) {
      return res.json({ spotify: false, tidal: false });
    }
    
    const now = Date.now();
    const spotifyValid = session.spotifyToken && (!session.spotifyTokenExpiry || session.spotifyTokenExpiry > now);
    const tidalValid = session.tidalCredentials && (!session.tidalTokenExpiry || session.tidalTokenExpiry > now);
    
    res.json({
      spotify: !!spotifyValid,
      tidal: !!tidalValid
    });
  } catch (error) {
    console.error('Auth status error:', error);
    res.status(500).json({ error: 'Failed to check auth status' });
  }
});

// Logout
router.post('/logout', async (req, res) => {
  try {
    const userId = req.body.userId || 'anonymous';
    
    // Clear user session
    const db = admin.firestore();
    await db.collection('sessions').doc(userId).delete();
    
    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({ error: 'Could not log out' });
  }
});

export { router as authRoutes };
