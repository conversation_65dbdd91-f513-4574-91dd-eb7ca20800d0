import express from 'express';
import axios from 'axios';
import * as admin from 'firebase-admin';
import { init as initApi } from '@tidal-music/api';

const router = express.Router();

// Helper function to get user session
async function getUserSession(userId: string) {
  const db = admin.firestore();
  const userDoc = await db.collection('sessions').doc(userId).get();
  return userDoc.exists ? userDoc.data() : null;
}

// Helper function to refresh Spotify token if needed
async function getValidSpotifyToken(userId: string, session: any) {
  if (!session.spotifyTokenExpiry || session.spotifyTokenExpiry > Date.now()) {
    return session.spotifyToken;
  }
  
  // Token expired, refresh it
  try {
    const response = await axios.post('https://accounts.spotify.com/api/token', 
      new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: session.spotifyRefreshToken,
        client_id: process.env.SPOTIFY_CLIENT_ID || '',
        client_secret: process.env.SPOTIFY_CLIENT_SECRET || ''
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    const newToken = response.data.access_token;
    const newExpiry = Date.now() + (response.data.expires_in * 1000);
    
    // Update session with new token
    const db = admin.firestore();
    await db.collection('sessions').doc(userId).update({
      spotifyToken: newToken,
      spotifyTokenExpiry: newExpiry
    });
    
    return newToken;
  } catch (error) {
    console.error('Failed to refresh Spotify token:', error);
    throw new Error('Token refresh failed');
  }
}

// Get Spotify playlists
router.get('/spotify', async (req, res) => {
  try {
    const userId = req.query.userId as string || 'anonymous';
    const session = await getUserSession(userId);
    
    if (!session || !session.spotifyToken) {
      return res.status(401).json({ error: 'Not authenticated with Spotify' });
    }
    
    const token = await getValidSpotifyToken(userId, session);
    
    const response = await axios.get('https://api.spotify.com/v1/me/playlists', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        limit: 50
      }
    });
    
    const playlists = response.data.items.map((playlist: any) => ({
      id: playlist.id,
      name: playlist.name,
      description: playlist.description,
      tracks: playlist.tracks.total,
      image: playlist.images?.[0]?.url,
      owner: playlist.owner.display_name,
      public: playlist.public
    }));
    
    res.json(playlists);
  } catch (error) {
    console.error('Error fetching Spotify playlists:', error);
    res.status(500).json({ error: 'Failed to fetch playlists' });
  }
});

// Get tracks from a specific Spotify playlist
router.get('/spotify/:playlistId/tracks', async (req, res) => {
  try {
    const userId = req.query.userId as string || 'anonymous';
    const session = await getUserSession(userId);
    
    if (!session || !session.spotifyToken) {
      return res.status(401).json({ error: 'Not authenticated with Spotify' });
    }
    
    const token = await getValidSpotifyToken(userId, session);
    const { playlistId } = req.params;
    const tracks = [];
    let url = `https://api.spotify.com/v1/playlists/${playlistId}/tracks`;
    
    while (url) {
      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          limit: 100
        }
      });
      
      const items = response.data.items
        .filter((item: any) => item.track && item.track.type === 'track')
        .map((item: any) => ({
          id: item.track.id,
          name: item.track.name,
          artist: item.track.artists.map((artist: any) => artist.name).join(', '),
          album: item.track.album.name,
          duration: item.track.duration_ms,
          preview_url: item.track.preview_url,
          external_urls: item.track.external_urls,
          isrc: item.track.external_ids?.isrc // Important for Tidal matching
        }));
      
      tracks.push(...items);
      url = response.data.next;
    }
    
    res.json(tracks);
  } catch (error) {
    console.error('Error fetching playlist tracks:', error);
    res.status(500).json({ error: 'Failed to fetch playlist tracks' });
  }
});

// Get Tidal playlists using Tidal SDK
router.get('/tidal', async (req, res) => {
  try {
    const userId = req.query.userId as string || 'anonymous';
    const session = await getUserSession(userId);
    
    if (!session || !session.tidalCredentials) {
      return res.status(401).json({ error: 'Not authenticated with Tidal' });
    }
    
    // Initialize Tidal API with user credentials
    const tidalApi = initApi({
      credentials: session.tidalCredentials
    });
    
    // Get user's playlists from Tidal
    const playlists = await tidalApi.getUserPlaylists();
    
    const formattedPlaylists = playlists.data.map((playlist: any) => ({
      id: playlist.id,
      name: playlist.title,
      description: playlist.description,
      tracks: playlist.numberOfTracks,
      image: playlist.image ? `https://resources.tidal.com/images/${playlist.image.replace(/-/g, '/')}/640x640.jpg` : null,
      owner: playlist.creator?.name || 'Unknown',
      public: playlist.publicPlaylist
    }));
    
    res.json(formattedPlaylists);
  } catch (error) {
    console.error('Error fetching Tidal playlists:', error);
    res.status(500).json({ error: 'Failed to fetch Tidal playlists' });
  }
});

export { router as playlistRoutes };
