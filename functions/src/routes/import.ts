import express from 'express';
import axios from 'axios';
import * as admin from 'firebase-admin';
import { init as initApi } from '@tidal-music/api';

const router = express.Router();

// Helper function to get user session
async function getUserSession(userId: string) {
  const db = admin.firestore();
  const userDoc = await db.collection('sessions').doc(userId).get();
  return userDoc.exists ? userDoc.data() : null;
}

// Helper function to get valid Spotify token
async function getValidSpotifyToken(userId: string, session: any) {
  if (!session.spotifyTokenExpiry || session.spotifyTokenExpiry > Date.now()) {
    return session.spotifyToken;
  }
  
  // Token expired, refresh it
  const response = await axios.post('https://accounts.spotify.com/api/token', 
    new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: session.spotifyRefreshToken,
      client_id: process.env.SPOTIFY_CLIENT_ID || '',
      client_secret: process.env.SPOTIFY_CLIENT_SECRET || ''
    }),
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }
  );
  
  const newToken = response.data.access_token;
  const newExpiry = Date.now() + (response.data.expires_in * 1000);
  
  // Update session
  const db = admin.firestore();
  await db.collection('sessions').doc(userId).update({
    spotifyToken: newToken,
    spotifyTokenExpiry: newExpiry
  });
  
  return newToken;
}

// Search for a track in Tidal
async function searchTidalTrack(tidalApi: any, track: any) {
  try {
    // Try searching by ISRC first (most accurate)
    if (track.isrc) {
      const isrcResults = await tidalApi.searchTracks({
        query: track.isrc,
        limit: 1
      });
      
      if (isrcResults.data && isrcResults.data.length > 0) {
        return isrcResults.data[0];
      }
    }
    
    // Fallback to artist + title search
    const query = `${track.artist} ${track.name}`.replace(/[^\w\s]/g, '').trim();
    const searchResults = await tidalApi.searchTracks({
      query: query,
      limit: 5
    });
    
    if (searchResults.data && searchResults.data.length > 0) {
      // Find best match based on similarity
      const bestMatch = searchResults.data.find((tidalTrack: any) => {
        const tidalTitle = tidalTrack.title.toLowerCase();
        const tidalArtist = tidalTrack.artist.name.toLowerCase();
        const spotifyTitle = track.name.toLowerCase();
        const spotifyArtist = track.artist.toLowerCase();
        
        return tidalTitle.includes(spotifyTitle.substring(0, 10)) || 
               spotifyTitle.includes(tidalTitle.substring(0, 10)) ||
               tidalArtist.includes(spotifyArtist.substring(0, 10));
      });
      
      return bestMatch || searchResults.data[0];
    }
    
    return null;
  } catch (error) {
    console.error('Error searching Tidal track:', error);
    return null;
  }
}

// Import playlists from Spotify to Tidal
router.post('/playlists', async (req, res) => {
  try {
    const { playlistIds, userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    const session = await getUserSession(userId);
    
    if (!session || !session.spotifyToken || !session.tidalCredentials) {
      return res.status(401).json({ error: 'Not authenticated with both services' });
    }
    
    if (!playlistIds || !Array.isArray(playlistIds)) {
      return res.status(400).json({ error: 'Invalid playlist IDs' });
    }
    
    const spotifyToken = await getValidSpotifyToken(userId, session);
    
    // Initialize Tidal API
    const tidalApi = initApi({
      credentials: session.tidalCredentials
    });
    
    const results = [];
    
    for (const playlistId of playlistIds) {
      try {
        // Get playlist details from Spotify
        const playlistResponse = await axios.get(`https://api.spotify.com/v1/playlists/${playlistId}`, {
          headers: {
            'Authorization': `Bearer ${spotifyToken}`
          }
        });
        
        const playlist = playlistResponse.data;
        
        // Get all tracks from the playlist
        const tracks = [];
        let url = `https://api.spotify.com/v1/playlists/${playlistId}/tracks`;
        
        while (url) {
          const tracksResponse = await axios.get(url, {
            headers: {
              'Authorization': `Bearer ${spotifyToken}`
            },
            params: { limit: 100 }
          });
          
          const items = tracksResponse.data.items
            .filter((item: any) => item.track && item.track.type === 'track')
            .map((item: any) => ({
              name: item.track.name,
              artist: item.track.artists.map((artist: any) => artist.name).join(', '),
              album: item.track.album.name,
              duration: item.track.duration_ms,
              isrc: item.track.external_ids?.isrc
            }));
          
          tracks.push(...items);
          url = tracksResponse.data.next;
        }
        
        // Create playlist in Tidal
        const tidalPlaylist = await tidalApi.createPlaylist({
          title: playlist.name,
          description: playlist.description || `Imported from Spotify: ${playlist.name}`
        });
        
        // Search and add tracks to Tidal playlist
        const tidalTrackIds = [];
        let matchedTracks = 0;
        
        for (const track of tracks) {
          const tidalTrack = await searchTidalTrack(tidalApi, track);
          if (tidalTrack) {
            tidalTrackIds.push(tidalTrack.id);
            matchedTracks++;
          }
          
          // Add small delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Add tracks to playlist in batches
        if (tidalTrackIds.length > 0) {
          const batchSize = 100;
          for (let i = 0; i < tidalTrackIds.length; i += batchSize) {
            const batch = tidalTrackIds.slice(i, i + batchSize);
            await tidalApi.addTracksToPlaylist(tidalPlaylist.id, batch);
          }
        }
        
        results.push({
          spotifyPlaylistId: playlistId,
          spotifyPlaylistName: playlist.name,
          tidalPlaylistId: tidalPlaylist.id,
          tidalPlaylistUrl: `https://tidal.com/playlist/${tidalPlaylist.id}`,
          tracksImported: matchedTracks,
          totalTracks: tracks.length,
          matchRate: Math.round((matchedTracks / tracks.length) * 100),
          success: true
        });
        
      } catch (error) {
        console.error(`Error importing playlist ${playlistId}:`, error);
        results.push({
          spotifyPlaylistId: playlistId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    res.json({
      success: true,
      results: results,
      summary: {
        total: playlistIds.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        totalTracksImported: results.reduce((sum, r) => sum + (r.tracksImported || 0), 0),
        totalTracksAttempted: results.reduce((sum, r) => sum + (r.totalTracks || 0), 0)
      }
    });
    
  } catch (error) {
    console.error('Import error:', error);
    res.status(500).json({ error: 'Failed to import playlists' });
  }
});

// Get import status (for real-time updates)
router.get('/status/:importId', (req, res) => {
  // For now, return completed status
  // In a real implementation, you might store import progress in Firestore
  res.json({
    importId: req.params.importId,
    status: 'completed',
    progress: 100,
    message: 'Import completed successfully'
  });
});

export { router as importRoutes };
