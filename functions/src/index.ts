import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import express from 'express';
import cors from 'cors';

// Initialize Firebase Admin
admin.initializeApp();

// Import route handlers
import { authRoutes } from './routes/auth';
import { playlistRoutes } from './routes/playlists';
import { importRoutes } from './routes/import';

// Create Express app
const app = express();

// Middleware
app.use(cors({
  origin: true, // Allow all origins for Firebase hosting
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/auth', authRoutes);
app.use('/playlists', playlistRoutes);
app.use('/import', importRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: 'Firebase Functions'
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({ error: 'Something went wrong!' });
});

// Export the Express app as a Firebase Function
export const api = functions.https.onRequest(app);
