# Firebase Deployment Guide

This guide will help you deploy the Playlist Importer app to Firebase.

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Firebase CLI** - Install with: `npm install -g firebase-tools`
3. **Firebase Project** - Create one at [Firebase Console](https://console.firebase.google.com)
4. **API Credentials** - Spotify and Tidal developer accounts

## Setup Steps

### 1. Firebase Project Setup

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project called `playlist-importer` (or your preferred name)
3. Enable the following services:
   - **Authentication** (for user sessions)
   - **Firestore Database** (for session storage)
   - **Functions** (for backend API)
   - **Hosting** (for frontend)

### 2. Local Setup

1. **Clone and install dependencies:**
   ```bash
   git clone <your-repo>
   cd playlist-importer
   npm run install-all
   ```

2. **Login to Firebase:**
   ```bash
   firebase login
   ```

3. **Initialize Firebase (if not already done):**
   ```bash
   firebase init
   ```
   Select:
   - Functions (TypeScript)
   - Firestore
   - Hosting

4. **Update Firebase project ID:**
   Edit `.firebaserc` and replace `playlist-importer` with your project ID.

### 3. API Credentials Setup

#### Spotify API
1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Add redirect URIs:
   - Development: `http://localhost:5001/playlist-importer/us-central1/api/auth/spotify/callback`
   - Production: `https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net/api/auth/spotify/callback`
4. Copy Client ID and Secret

#### Tidal API
1. Go to [Tidal Developer Portal](https://developer.tidal.com)
2. Create a new app
3. Add redirect URIs:
   - Development: `http://localhost:5001/playlist-importer/us-central1/api/auth/tidal/callback`
   - Production: `https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net/api/auth/tidal/callback`
4. Copy Client ID and Secret

### 4. Environment Configuration

1. **Set Firebase Functions environment variables:**
   ```bash
   firebase functions:config:set \
     spotify.client_id="your_spotify_client_id" \
     spotify.client_secret="your_spotify_client_secret" \
     tidal.client_id="your_tidal_client_id" \
     tidal.client_secret="your_tidal_client_secret" \
     app.client_url="https://YOUR_PROJECT_ID.web.app" \
     app.function_url="https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net"
   ```

2. **Update client environment:**
   Create `client/.env.production`:
   ```
   REACT_APP_API_BASE=/api
   ```

### 5. Firestore Security Rules

Update `firestore.rules`:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read/write their own session data
    match /sessions/{userId} {
      allow read, write: if true; // In production, add proper auth
    }
  }
}
```

### 6. Deploy to Firebase

1. **Deploy everything:**
   ```bash
   npm run firebase:deploy
   ```

   Or deploy separately:
   ```bash
   # Deploy functions only
   npm run firebase:functions
   
   # Deploy hosting only
   npm run firebase:hosting
   ```

2. **Update redirect URIs:**
   After deployment, update your Spotify and Tidal app redirect URIs with the production URLs.

### 7. Testing

1. **Local testing with emulators:**
   ```bash
   npm run firebase:serve
   ```
   - Functions: http://localhost:5001
   - Hosting: http://localhost:5000
   - Emulator UI: http://localhost:4000

2. **Production testing:**
   Visit your deployed app at `https://YOUR_PROJECT_ID.web.app`

## Environment Variables Reference

### Firebase Functions Config
```bash
# Set all at once
firebase functions:config:set \
  spotify.client_id="..." \
  spotify.client_secret="..." \
  tidal.client_id="..." \
  tidal.client_secret="..." \
  app.client_url="https://YOUR_PROJECT_ID.web.app" \
  app.function_url="https://us-central1-YOUR_PROJECT_ID.cloudfunctions.net"

# View current config
firebase functions:config:get
```

### Client Environment
Create `client/.env.production`:
```
REACT_APP_API_BASE=/api
```

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Ensure your client URL is correctly set in functions config
   - Check that redirect URIs match exactly in Spotify/Tidal apps

2. **Function Timeout:**
   - Increase timeout in `firebase.json` functions config
   - Optimize import logic for large playlists

3. **Authentication Issues:**
   - Verify API credentials are correctly set
   - Check redirect URIs in developer consoles
   - Ensure Firestore rules allow session access

4. **Build Errors:**
   - Run `cd functions && npm run build` to check TypeScript errors
   - Ensure all dependencies are installed

### Useful Commands

```bash
# View function logs
firebase functions:log

# Deploy specific function
firebase deploy --only functions:api

# View Firestore data
firebase firestore:indexes

# Reset functions config
firebase functions:config:unset spotify tidal app
```

## Production Considerations

1. **Security:**
   - Implement proper Firestore security rules
   - Add rate limiting to functions
   - Validate all inputs

2. **Performance:**
   - Implement caching for API responses
   - Use Firestore for session persistence
   - Add error retry logic

3. **Monitoring:**
   - Set up Firebase Performance Monitoring
   - Configure error reporting
   - Monitor function execution times

4. **Scaling:**
   - Consider using Firebase Auth for user management
   - Implement proper session cleanup
   - Add request queuing for large imports
