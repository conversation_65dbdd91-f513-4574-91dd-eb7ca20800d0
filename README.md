# 🎵 Playlist Importer

A modern web application that allows users to seamlessly import playlists from Spotify to Tidal. Built with React and Node.js, featuring a responsive design that works perfectly on both desktop and mobile devices.

## ✨ Features

- **Dual Authentication**: Secure OAuth integration with both Spotify and Tidal
- **Smart Playlist Selection**: Browse, search, and multi-select Spotify playlists
- **Batch Import**: Import multiple playlists simultaneously
- **Progress Tracking**: Real-time import progress with detailed results
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Track Matching**: Intelligent track matching between Spotify and Tidal catalogs

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Spotify Developer Account
- Tidal Account (Note: Uses mock API for demonstration)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd playlist-importer
   ```

2. **Install dependencies**
   ```bash
   npm run install-all
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your API credentials (see setup guide below)

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001

## 🔧 API Credentials Setup

### Spotify API Setup
1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Click "Create App"
3. Fill in app details:
   - **App Name**: Playlist Importer
   - **App Description**: Import playlists from Spotify to Tidal
   - **Redirect URI**: `http://localhost:3001/auth/spotify/callback`
4. Copy your **Client ID** and **Client Secret** to `.env`:
   ```
   SPOTIFY_CLIENT_ID=your_spotify_client_id
   SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
   ```

### Tidal Integration
**Important**: Tidal doesn't provide a public API for third-party developers. This application includes a mock Tidal API implementation for demonstration purposes. In a real-world scenario, you would need:
- Official partnership with Tidal
- Access to their private API
- Proper authentication flow

For the demo, the mock implementation simulates:
- Tidal authentication
- Playlist creation
- Track searching and matching

## 🏗️ Project Structure

```
playlist-importer/
├── server/                 # Express.js Backend
│   ├── index.js           # Main server file
│   └── routes/            # API routes
│       ├── auth.js        # Authentication endpoints
│       ├── playlists.js   # Playlist management
│       └── import.js      # Import functionality
├── client/                # React Frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   │   ├── AuthSection.js
│   │   │   ├── PlaylistSelector.js
│   │   │   └── ImportProgress.js
│   │   ├── App.js         # Main app component
│   │   └── App.css        # Global styles
│   └── public/            # Static assets
├── package.json           # Root dependencies
├── .env.example          # Environment template
└── README.md             # This file
```

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run server` - Start only the backend server
- `npm run client` - Start only the frontend development server
- `npm run build` - Build the frontend for production
- `npm start` - Start the production server

### Testing the Setup

Run the test script to verify your server is working:
```bash
node test-server.js
```

### Environment Variables

Create a `.env` file in the root directory:

```env
# Spotify API credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret

# Tidal API credentials (mock for demo)
TIDAL_CLIENT_ID=demo_tidal_client_id
TIDAL_CLIENT_SECRET=demo_tidal_client_secret

# Session configuration
SESSION_SECRET=your_random_session_secret_here

# Server configuration
PORT=3001
CLIENT_URL=http://localhost:3000
SERVER_URL=http://localhost:3001
```

## 📱 Mobile Responsiveness

The application is fully responsive and optimized for:
- **Desktop**: Full-featured experience with grid layouts
- **Tablet**: Adapted layouts with touch-friendly controls
- **Mobile**: Single-column layouts with optimized navigation

Key responsive features:
- Flexible grid systems that adapt to screen size
- Touch-friendly buttons and controls
- Optimized typography and spacing
- Mobile-first CSS approach

## 🔄 How It Works

1. **Authentication**: Users authenticate with both Spotify and Tidal
2. **Playlist Discovery**: Fetch and display user's Spotify playlists
3. **Selection**: Users can search, filter, and select multiple playlists
4. **Import Process**:
   - Retrieve track details from selected Spotify playlists
   - Search for matching tracks in Tidal's catalog
   - Create new playlists in Tidal
   - Add matched tracks to the new playlists
5. **Results**: Display import summary with success/failure details

## 🚨 Important Notes

### Tidal API Limitations
- Tidal does not provide a public API for third-party developers
- This implementation uses a mock API for demonstration purposes
- Track matching is simulated (85% success rate in demo)
- Real implementation would require official Tidal partnership

### Spotify API Rate Limits
- Spotify API has rate limits (typically 100 requests per minute)
- Large playlists may take time to process
- The app handles pagination automatically for playlists with 100+ tracks

### Data Privacy
- No playlist data is stored permanently
- Authentication tokens are session-based
- All data processing happens in real-time

## 🐛 Troubleshooting

### Common Issues

**Server won't start**
```bash
# Check if port 3001 is already in use
lsof -i :3001
# Kill the process if needed
kill -9 <PID>
```

**Spotify authentication fails**
- Verify your Spotify app settings
- Ensure redirect URI matches exactly: `http://localhost:3001/auth/spotify/callback`
- Check that your Client ID and Secret are correct in `.env`

**Frontend can't connect to backend**
- Ensure both servers are running (`npm run dev`)
- Check that CORS is properly configured
- Verify API_BASE URL in frontend matches backend port

**Import process fails**
- Check browser console for error messages
- Verify both Spotify and Tidal authentication are successful
- Ensure selected playlists contain tracks

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Spotify Web API for playlist access
- React team for the amazing framework
- Express.js for the robust backend framework
- All contributors and testers

---

**Note**: This is a demonstration project. For production use, you would need official API partnerships with music streaming services.
