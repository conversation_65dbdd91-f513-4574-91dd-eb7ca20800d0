[debug] [2025-08-18T16:33:05.230Z] ----------------------------------------------------------------------
[debug] [2025-08-18T16:33:05.232Z] Command:       /Users/<USER>/.nvm/versions/node/v21.7.2/bin/node /Users/<USER>/.nvm/versions/node/v21.7.2/bin/firebase init
[debug] [2025-08-18T16:33:05.232Z] CLI Version:   14.12.1
[debug] [2025-08-18T16:33:05.232Z] Platform:      darwin
[debug] [2025-08-18T16:33:05.232Z] Node Version:  v21.7.2
[debug] [2025-08-18T16:33:05.232Z] Time:          Mon Aug 18 2025 18:33:05 GMT+0200 (Central European Summer Time)
[debug] [2025-08-18T16:33:05.232Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-18T16:33:05.338Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-08-18T16:33:05.338Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Sites/playlist-importer

Before we get started, keep in mind:

  * You are initializing within an existing Firebase project directory

[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[debug] [2025-08-18T16:33:10.941Z] Using project from CLI flag: playlist-importer
[debug] [2025-08-18T16:33:10.942Z] Checked if tokens are valid: true, expires at: 1755538183715
[debug] [2025-08-18T16:33:10.942Z] Checked if tokens are valid: true, expires at: 1755538183715
[debug] [2025-08-18T16:33:10.942Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/playlist-importer [none]
[debug] [2025-08-18T16:33:11.377Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/playlist-importer 403
[debug] [2025-08-18T16:33:11.377Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/playlist-importer {"error":{"code":403,"message":"The caller does not have permission","status":"PERMISSION_DENIED"}}
[debug] [2025-08-18T16:33:11.377Z] Request to https://firebase.googleapis.com/v1beta1/projects/playlist-importer had HTTP Error: 403, The caller does not have permission
[debug] [2025-08-18T16:33:11.488Z] FirebaseError: Request to https://firebase.googleapis.com/v1beta1/projects/playlist-importer had HTTP Error: 403, The caller does not have permission
    at responseToError (/Users/<USER>/.nvm/versions/node/v21.7.2/lib/node_modules/firebase-tools/lib/responseToError.js:52:12)
    at RetryOperation._fn (/Users/<USER>/.nvm/versions/node/v21.7.2/lib/node_modules/firebase-tools/lib/apiv2.js:312:77)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
[error] 
[error] Error: Failed to get Firebase project playlist-importer. Please make sure the project exists and your account has permission to access it.
