{"name": "playlist-importer", "version": "1.0.0", "description": "A webapp to import playlists from Spotify to Tidal", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/index.js", "client": "cd client && npm start", "build": "cd client && npm run build", "start": "node server/index.js", "install-all": "npm install && cd client && npm install", "test": "node test-server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.5.0", "express-session": "^1.17.3", "passport": "^0.6.0", "passport-spotify": "^2.0.0", "querystring": "^0.2.1", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.1"}, "keywords": ["spotify", "tidal", "playlist", "import", "music"], "author": "", "license": "MIT"}